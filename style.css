:root {
  /* Primitive Color Tokens */
  --color-white: rgba(255, 255, 255, 1);
  --color-black: rgba(0, 0, 0, 1);
  --color-cream-50: rgba(252, 252, 249, 1);
  --color-cream-100: rgba(255, 255, 253, 1);
  --color-gray-200: rgba(245, 245, 245, 1);
  --color-gray-300: rgba(167, 169, 169, 1);
  --color-gray-400: rgba(119, 124, 124, 1);
  --color-slate-500: rgba(98, 108, 113, 1);
  --color-brown-600: rgba(94, 82, 64, 1);
  --color-charcoal-700: rgba(31, 33, 33, 1);
  --color-charcoal-800: rgba(38, 40, 40, 1);
  --color-slate-900: rgba(19, 52, 59, 1);
  --color-teal-300: rgba(50, 184, 198, 1);
  --color-teal-400: rgba(45, 166, 178, 1);
  --color-teal-500: rgba(33, 128, 141, 1);
  --color-teal-600: rgba(29, 116, 128, 1);
  --color-teal-700: rgba(26, 104, 115, 1);
  --color-teal-800: rgba(41, 150, 161, 1);
  --color-red-400: rgba(255, 84, 89, 1);
  --color-red-500: rgba(192, 21, 47, 1);
  --color-orange-400: rgba(230, 129, 97, 1);
  --color-orange-500: rgba(168, 75, 47, 1);

  /* RGB versions for opacity control */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  --color-slate-500-rgb: 98, 108, 113;
  --color-red-500-rgb: 192, 21, 47;
  --color-red-400-rgb: 255, 84, 89;
  --color-orange-500-rgb: 168, 75, 47;
  --color-orange-400-rgb: 230, 129, 97;

  /* Background color tokens (Light Mode) */
  --color-bg-1: rgba(59, 130, 246, 0.08); /* Light blue */
  --color-bg-2: rgba(245, 158, 11, 0.08); /* Light yellow */
  --color-bg-3: rgba(34, 197, 94, 0.08); /* Light green */
  --color-bg-4: rgba(239, 68, 68, 0.08); /* Light red */
  --color-bg-5: rgba(147, 51, 234, 0.08); /* Light purple */
  --color-bg-6: rgba(249, 115, 22, 0.08); /* Light orange */
  --color-bg-7: rgba(236, 72, 153, 0.08); /* Light pink */
  --color-bg-8: rgba(6, 182, 212, 0.08); /* Light cyan */

  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);
  --color-select-caret: rgba(var(--color-slate-900-rgb), 0.8);

  /* Common style patterns */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for opacity control */
  --color-success-rgb: 33, 128, 141;
  --color-error-rgb: 192, 21, 47;
  --color-warning-rgb: 168, 75, 47;
  --color-info-rgb: 98, 108, 113;

  /* Typography */
  --font-family-base: "FKGroteskNeue", "Geist", "Inter", -apple-system,
    BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  --font-family-mono: "Berkeley Mono", ui-monospace, SFMono-Regular, Menlo,
    Monaco, Consolas, monospace;
  --font-size-xs: 11px;
  --font-size-sm: 12px;
  --font-size-base: 14px;
  --font-size-md: 14px;
  --font-size-lg: 16px;
  --font-size-xl: 18px;
  --font-size-2xl: 20px;
  --font-size-3xl: 24px;
  --font-size-4xl: 30px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 550;
  --font-weight-bold: 600;
  --line-height-tight: 1.2;
  --line-height-normal: 1.5;
  --letter-spacing-tight: -0.01em;

  /* Spacing */
  --space-0: 0;
  --space-1: 1px;
  --space-2: 2px;
  --space-4: 4px;
  --space-6: 6px;
  --space-8: 8px;
  --space-10: 10px;
  --space-12: 12px;
  --space-16: 16px;
  --space-20: 20px;
  --space-24: 24px;
  --space-32: 32px;
  --space-40: 40px;
  --space-48: 48px;
  --space-64: 64px;

  /* Border Radius */
  --radius-sm: 6px;
  --radius-base: 8px;
  --radius-md: 10px;
  --radius-lg: 12px;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.04), 0 1px 2px rgba(0, 0, 0, 0.02);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.04),
    0 2px 4px -1px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.04),
    0 4px 6px -2px rgba(0, 0, 0, 0.02);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.15),
    inset 0 -1px 0 rgba(0, 0, 0, 0.03);

  /* Animation */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --ease-standard: cubic-bezier(0.16, 1, 0.3, 1);

  /* Layout */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
}

/* Dark mode colors */
@media (prefers-color-scheme: dark) {
  :root {
    /* RGB versions for opacity control (Dark Mode) */
    --color-gray-400-rgb: 119, 124, 124;
    --color-teal-300-rgb: 50, 184, 198;
    --color-gray-300-rgb: 167, 169, 169;
    --color-gray-200-rgb: 245, 245, 245;

    /* Background color tokens (Dark Mode) */
    --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
    --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
    --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
    --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
    --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
    --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
    --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
    --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
    
    /* Semantic Color Tokens (Dark Mode) */
    --color-background: var(--color-charcoal-700);
    --color-surface: var(--color-charcoal-800);
    --color-text: var(--color-gray-200);
    --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
    --color-primary: var(--color-teal-300);
    --color-primary-hover: var(--color-teal-400);
    --color-primary-active: var(--color-teal-800);
    --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
    --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
    --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
    --color-border: rgba(var(--color-gray-400-rgb), 0.3);
    --color-error: var(--color-red-400);
    --color-success: var(--color-teal-300);
    --color-warning: var(--color-orange-400);
    --color-info: var(--color-gray-300);
    --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
    --color-btn-primary-text: var(--color-slate-900);
    --color-card-border: rgba(var(--color-gray-400-rgb), 0.2);
    --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
    --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(0, 0, 0, 0.15);
    --button-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
    --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

    /* Common style patterns - updated for dark mode */
    --focus-ring: 0 0 0 3px var(--color-focus-ring);
    --focus-outline: 2px solid var(--color-primary);
    --status-bg-opacity: 0.15;
    --status-border-opacity: 0.25;
    --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
    --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

    /* RGB versions for dark mode */
    --color-success-rgb: var(--color-teal-300-rgb);
    --color-error-rgb: var(--color-red-400-rgb);
    --color-warning-rgb: var(--color-orange-400-rgb);
    --color-info-rgb: var(--color-gray-300-rgb);
  }
}

/* Data attribute for manual theme switching */
[data-color-scheme="dark"] {
  /* RGB versions for opacity control (dark mode) */
  --color-gray-400-rgb: 119, 124, 124;
  --color-teal-300-rgb: 50, 184, 198;
  --color-gray-300-rgb: 167, 169, 169;
  --color-gray-200-rgb: 245, 245, 245;

  /* Colorful background palette - Dark Mode */
  --color-bg-1: rgba(29, 78, 216, 0.15); /* Dark blue */
  --color-bg-2: rgba(180, 83, 9, 0.15); /* Dark yellow */
  --color-bg-3: rgba(21, 128, 61, 0.15); /* Dark green */
  --color-bg-4: rgba(185, 28, 28, 0.15); /* Dark red */
  --color-bg-5: rgba(107, 33, 168, 0.15); /* Dark purple */
  --color-bg-6: rgba(194, 65, 12, 0.15); /* Dark orange */
  --color-bg-7: rgba(190, 24, 93, 0.15); /* Dark pink */
  --color-bg-8: rgba(8, 145, 178, 0.15); /* Dark cyan */
  
  /* Semantic Color Tokens (Dark Mode) */
  --color-background: var(--color-charcoal-700);
  --color-surface: var(--color-charcoal-800);
  --color-text: var(--color-gray-200);
  --color-text-secondary: rgba(var(--color-gray-300-rgb), 0.7);
  --color-primary: var(--color-teal-300);
  --color-primary-hover: var(--color-teal-400);
  --color-primary-active: var(--color-teal-800);
  --color-secondary: rgba(var(--color-gray-400-rgb), 0.15);
  --color-secondary-hover: rgba(var(--color-gray-400-rgb), 0.25);
  --color-secondary-active: rgba(var(--color-gray-400-rgb), 0.3);
  --color-border: rgba(var(--color-gray-400-rgb), 0.3);
  --color-error: var(--color-red-400);
  --color-success: var(--color-teal-300);
  --color-warning: var(--color-orange-400);
  --color-info: var(--color-gray-300);
  --color-focus-ring: rgba(var(--color-teal-300-rgb), 0.4);
  --color-btn-primary-text: var(--color-slate-900);
  --color-card-border: rgba(var(--color-gray-400-rgb), 0.15);
  --color-card-border-inner: rgba(var(--color-gray-400-rgb), 0.15);
  --shadow-inset-sm: inset 0 1px 0 rgba(255, 255, 255, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.15);
  --color-border-secondary: rgba(var(--color-gray-400-rgb), 0.2);
  --color-select-caret: rgba(var(--color-gray-200-rgb), 0.8);

  /* Common style patterns - updated for dark mode */
  --focus-ring: 0 0 0 3px var(--color-focus-ring);
  --focus-outline: 2px solid var(--color-primary);
  --status-bg-opacity: 0.15;
  --status-border-opacity: 0.25;
  --select-caret-light: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23134252' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  --select-caret-dark: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23f5f5f5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");

  /* RGB versions for dark mode */
  --color-success-rgb: var(--color-teal-300-rgb);
  --color-error-rgb: var(--color-red-400-rgb);
  --color-warning-rgb: var(--color-orange-400-rgb);
  --color-info-rgb: var(--color-gray-300-rgb);
}

[data-color-scheme="light"] {
  /* RGB versions for opacity control (light mode) */
  --color-brown-600-rgb: 94, 82, 64;
  --color-teal-500-rgb: 33, 128, 141;
  --color-slate-900-rgb: 19, 52, 59;
  
  /* Semantic Color Tokens (Light Mode) */
  --color-background: var(--color-cream-50);
  --color-surface: var(--color-cream-100);
  --color-text: var(--color-slate-900);
  --color-text-secondary: var(--color-slate-500);
  --color-primary: var(--color-teal-500);
  --color-primary-hover: var(--color-teal-600);
  --color-primary-active: var(--color-teal-700);
  --color-secondary: rgba(var(--color-brown-600-rgb), 0.12);
  --color-secondary-hover: rgba(var(--color-brown-600-rgb), 0.2);
  --color-secondary-active: rgba(var(--color-brown-600-rgb), 0.25);
  --color-border: rgba(var(--color-brown-600-rgb), 0.2);
  --color-btn-primary-text: var(--color-cream-50);
  --color-card-border: rgba(var(--color-brown-600-rgb), 0.12);
  --color-card-border-inner: rgba(var(--color-brown-600-rgb), 0.12);
  --color-error: var(--color-red-500);
  --color-success: var(--color-teal-500);
  --color-warning: var(--color-orange-500);
  --color-info: var(--color-slate-500);
  --color-focus-ring: rgba(var(--color-teal-500-rgb), 0.4);

  /* RGB versions for light mode */
  --color-success-rgb: var(--color-teal-500-rgb);
  --color-error-rgb: var(--color-red-500-rgb);
  --color-warning-rgb: var(--color-orange-500-rgb);
  --color-info-rgb: var(--color-slate-500-rgb);
}

/* Base styles */
html {
  font-size: var(--font-size-base);
  font-family: var(--font-family-base);
  line-height: var(--line-height-normal);
  color: var(--color-text);
  background-color: var(--color-background);
  -webkit-font-smoothing: antialiased;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-tight);
  color: var(--color-text);
  letter-spacing: var(--letter-spacing-tight);
}

h1 {
  font-size: var(--font-size-4xl);
}
h2 {
  font-size: var(--font-size-3xl);
}
h3 {
  font-size: var(--font-size-2xl);
}
h4 {
  font-size: var(--font-size-xl);
}
h5 {
  font-size: var(--font-size-lg);
}
h6 {
  font-size: var(--font-size-md);
}

p {
  margin: 0 0 var(--space-16) 0;
}

a {
  color: var(--color-primary);
  text-decoration: none;
  transition: color var(--duration-fast) var(--ease-standard);
}

a:hover {
  color: var(--color-primary-hover);
}

code,
pre {
  font-family: var(--font-family-mono);
  font-size: calc(var(--font-size-base) * 0.95);
  background-color: var(--color-secondary);
  border-radius: var(--radius-sm);
}

code {
  padding: var(--space-1) var(--space-4);
}

pre {
  padding: var(--space-16);
  margin: var(--space-16) 0;
  overflow: auto;
  border: 1px solid var(--color-border);
}

pre code {
  background: none;
  padding: 0;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-16);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: 500;
  line-height: 1.5;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  text-decoration: none;
  position: relative;
}

.btn:focus-visible {
  outline: none;
  box-shadow: var(--focus-ring);
}

.btn--primary {
  background: var(--color-primary);
  color: var(--color-btn-primary-text);
}

.btn--primary:hover {
  background: var(--color-primary-hover);
}

.btn--primary:active {
  background: var(--color-primary-active);
}

.btn--secondary {
  background: var(--color-secondary);
  color: var(--color-text);
}

.btn--secondary:hover {
  background: var(--color-secondary-hover);
}

.btn--secondary:active {
  background: var(--color-secondary-active);
}

.btn--outline {
  background: transparent;
  border: 1px solid var(--color-border);
  color: var(--color-text);
}

.btn--outline:hover {
  background: var(--color-secondary);
}

.btn--sm {
  padding: var(--space-4) var(--space-12);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-sm);
}

.btn--lg {
  padding: var(--space-10) var(--space-20);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-md);
}

.btn--full-width {
  width: 100%;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Form elements */
.form-control {
  display: block;
  width: 100%;
  padding: var(--space-8) var(--space-12);
  font-size: var(--font-size-md);
  line-height: 1.5;
  color: var(--color-text);
  background-color: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-base);
  transition: border-color var(--duration-fast) var(--ease-standard),
    box-shadow var(--duration-fast) var(--ease-standard);
}

textarea.form-control {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
}

select.form-control {
  padding: var(--space-8) var(--space-12);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: var(--select-caret-light);
  background-repeat: no-repeat;
  background-position: right var(--space-12) center;
  background-size: 16px;
  padding-right: var(--space-32);
}

/* Add a dark mode specific caret */
@media (prefers-color-scheme: dark) {
  select.form-control {
    background-image: var(--select-caret-dark);
  }
}

/* Also handle data-color-scheme */
[data-color-scheme="dark"] select.form-control {
  background-image: var(--select-caret-dark);
}

[data-color-scheme="light"] select.form-control {
  background-image: var(--select-caret-light);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: var(--focus-outline);
}

.form-label {
  display: block;
  margin-bottom: var(--space-8);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.form-group {
  margin-bottom: var(--space-16);
}

/* Card component */
.card {
  background-color: var(--color-surface);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-card-border);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: box-shadow var(--duration-normal) var(--ease-standard);
}

.card:hover {
  box-shadow: var(--shadow-md);
}

.card__body {
  padding: var(--space-16);
}

.card__header,
.card__footer {
  padding: var(--space-16);
  border-bottom: 1px solid var(--color-card-border-inner);
}

/* Status indicators - simplified with CSS variables */
.status {
  display: inline-flex;
  align-items: center;
  padding: var(--space-6) var(--space-12);
  border-radius: var(--radius-full);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
}

.status--success {
  background-color: rgba(
    var(--color-success-rgb, 33, 128, 141),
    var(--status-bg-opacity)
  );
  color: var(--color-success);
  border: 1px solid
    rgba(var(--color-success-rgb, 33, 128, 141), var(--status-border-opacity));
}

.status--error {
  background-color: rgba(
    var(--color-error-rgb, 192, 21, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-error);
  border: 1px solid
    rgba(var(--color-error-rgb, 192, 21, 47), var(--status-border-opacity));
}

.status--warning {
  background-color: rgba(
    var(--color-warning-rgb, 168, 75, 47),
    var(--status-bg-opacity)
  );
  color: var(--color-warning);
  border: 1px solid
    rgba(var(--color-warning-rgb, 168, 75, 47), var(--status-border-opacity));
}

.status--info {
  background-color: rgba(
    var(--color-info-rgb, 98, 108, 113),
    var(--status-bg-opacity)
  );
  color: var(--color-info);
  border: 1px solid
    rgba(var(--color-info-rgb, 98, 108, 113), var(--status-border-opacity));
}

/* Container layout */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: var(--space-16);
  padding-left: var(--space-16);
}

@media (min-width: 640px) {
  .container {
    max-width: var(--container-sm);
  }
}
@media (min-width: 768px) {
  .container {
    max-width: var(--container-md);
  }
}
@media (min-width: 1024px) {
  .container {
    max-width: var(--container-lg);
  }
}
@media (min-width: 1280px) {
  .container {
    max-width: var(--container-xl);
  }
}

/* Utility classes */
.flex {
  display: flex;
}
.flex-col {
  flex-direction: column;
}
.items-center {
  align-items: center;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-4 {
  gap: var(--space-4);
}
.gap-8 {
  gap: var(--space-8);
}
.gap-16 {
  gap: var(--space-16);
}

.m-0 {
  margin: 0;
}
.mt-8 {
  margin-top: var(--space-8);
}
.mb-8 {
  margin-bottom: var(--space-8);
}
.mx-8 {
  margin-left: var(--space-8);
  margin-right: var(--space-8);
}
.my-8 {
  margin-top: var(--space-8);
  margin-bottom: var(--space-8);
}

.p-0 {
  padding: 0;
}
.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}
.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}
.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}
.px-16 {
  padding-left: var(--space-16);
  padding-right: var(--space-16);
}

.block {
  display: block;
}
.hidden {
  display: none;
}

/* Accessibility */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}

:focus-visible {
  outline: var(--focus-outline);
  outline-offset: 2px;
}

/* Dark mode specifics */
[data-color-scheme="dark"] .btn--outline {
  border: 1px solid var(--color-border-secondary);
}

@font-face {
  font-family: 'FKGroteskNeue';
  src: url('https://r2cdn.perplexity.ai/fonts/FKGroteskNeue.woff2')
    format('woff2');
}

/* END PERPLEXITY DESIGN SYSTEM */
/* Custom theme overrides for portfolio */
:root {
  /* Portfolio-specific color palette */
  --portfolio-bg: #0a0a0a;
  --portfolio-secondary: #1a1a1a;
  --portfolio-text-primary: #e5e5e5;
  --portfolio-text-secondary: #a0a0a0;
  --portfolio-accent: #64ffda;
  --portfolio-surface: #1e1e1e;
  
  /* Override design system colors for this theme */
  --color-background: var(--portfolio-bg);
  --color-surface: var(--portfolio-surface);
  --color-text: var(--portfolio-text-primary);
  --color-text-secondary: var(--portfolio-text-secondary);
  --color-primary: var(--portfolio-accent);
  --color-primary-hover: #4fd1c7;
  --color-primary-active: #3bc4b8;
}

/* Base styles with Inter font */
html {
  font-family: 'Inter', var(--font-family-base);
  background-color: var(--portfolio-bg);
  color: var(--portfolio-text-primary);
  overflow-x: hidden;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: var(--portfolio-bg);
}

/* Remove default focus outlines and blue dots */
* {
  outline: none !important;
  -webkit-tap-highlight-color: transparent !important;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Allow text selection for content */
.hero-name, .hero-bio, .placeholder-text {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Navigation Styles */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(10, 10, 10, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(100, 255, 218, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateY(0);
}

.navbar.scrolled {
  background: rgba(10, 10, 10, 0.98);
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.3);
}

.navbar.hidden {
  transform: translateY(-100%);
}

.nav-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-16);
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

/* Brand/Logo */
.nav-brand {
  position: relative;
  z-index: 1001;
}

.brand-link {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.brand-text {
  font-size: var(--font-size-xl);
  font-weight: 700;
  color: var(--portfolio-text-primary);
  background: linear-gradient(135deg, var(--portfolio-text-primary) 0%, var(--portfolio-accent) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.brand-dot {
  width: 8px;
  height: 8px;
  background: var(--portfolio-accent);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.brand-link:hover .brand-dot {
  transform: scale(1.5);
  box-shadow: 0 0 20px var(--portfolio-accent);
}

/* Desktop Navigation */
.nav-menu {
  display: none;
}

@media (min-width: 768px) {
  .nav-menu {
    display: block;
  }
}

.nav-list {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-item {
  position: relative;
}

.nav-link {
  position: relative;
  display: flex;
  align-items: center;
  padding: var(--space-12) var(--space-16);
  color: var(--portfolio-text-secondary);
  text-decoration: none;
  font-weight: 500;
  font-size: var(--font-size-sm);
  border-radius: var(--radius-base);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.nav-text {
  position: relative;
  z-index: 2;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-indicator {
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--portfolio-accent);
  transform: translateX(-50%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.nav-link::before {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(100, 255, 218, 0.1);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.nav-link:hover {
  color: var(--portfolio-accent);
  transform: translateY(-2px);
}

.nav-link:hover::before {
  opacity: 1;
}

.nav-link:hover .nav-indicator {
  width: 100%;
}

.nav-link.active {
  color: var(--portfolio-accent);
}

.nav-link.active .nav-indicator {
  width: 100%;
}

.nav-link.active::before {
  opacity: 0.5;
}

/* Hamburger Menu */
.nav-hamburger {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 30px;
  height: 30px;
  cursor: pointer;
  position: relative;
  z-index: 1001;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (min-width: 768px) {
  .nav-hamburger {
    display: none;
  }
}

.hamburger-line {
  width: 25px;
  height: 2px;
  background: var(--portfolio-text-primary);
  margin: 3px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.nav-hamburger:hover .hamburger-line {
  background: var(--portfolio-accent);
}

.nav-hamburger.active .hamburger-line:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.nav-hamburger.active .hamburger-line:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.nav-hamburger.active .hamburger-line:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(10, 10, 10, 0.98);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

.mobile-menu-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--space-32);
}

.mobile-nav-list {
  list-style: none;
  margin: 0;
  padding: 0;
  text-align: center;
}

.mobile-nav-item {
  margin-bottom: var(--space-24);
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-overlay.active .mobile-nav-item {
  opacity: 1;
  transform: translateY(0);
}

.mobile-menu-overlay.active .mobile-nav-item:nth-child(1) { transition-delay: 0.1s; }
.mobile-menu-overlay.active .mobile-nav-item:nth-child(2) { transition-delay: 0.2s; }
.mobile-menu-overlay.active .mobile-nav-item:nth-child(3) { transition-delay: 0.3s; }
.mobile-menu-overlay.active .mobile-nav-item:nth-child(4) { transition-delay: 0.4s; }
.mobile-menu-overlay.active .mobile-nav-item:nth-child(5) { transition-delay: 0.5s; }
.mobile-menu-overlay.active .mobile-nav-item:nth-child(6) { transition-delay: 0.6s; }

.mobile-nav-link {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-16);
  padding: var(--space-16) var(--space-24);
  color: var(--portfolio-text-secondary);
  text-decoration: none;
  font-size: var(--font-size-xl);
  font-weight: 600;
  border-radius: var(--radius-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.mobile-nav-number {
  font-size: var(--font-size-sm);
  color: var(--portfolio-accent);
  font-weight: 400;
  opacity: 0.7;
}

.mobile-nav-text {
  position: relative;
  z-index: 2;
}

.mobile-nav-link::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(100, 255, 218, 0.1) 0%, rgba(100, 255, 218, 0.05) 100%);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
}

.mobile-nav-link:hover {
  color: var(--portfolio-accent);
  transform: scale(1.05);
}

.mobile-nav-link:hover::before {
  opacity: 1;
}

.mobile-nav-link:hover .mobile-nav-number {
  opacity: 1;
  transform: scale(1.1);
}

.mobile-nav-link.active {
  color: var(--portfolio-accent);
}

.mobile-nav-link.active::before {
  opacity: 0.7;
}

.mobile-nav-link.active .mobile-nav-number {
  opacity: 1;
}

/* Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-32) var(--space-16);
  position: relative;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(100, 255, 218, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 1;
}

/* Two-column layout */
.hero-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-32);
  align-items: center;
}

@media (min-width: 768px) {
  .hero-content {
    grid-template-columns: 1fr 1.2fr;
    gap: var(--space-32);
  }
}

/* Image Column */
.image-column {
  display: flex;
  justify-content: center;
  align-items: center;
}

.image-placeholder {
  width: 280px;
  height: 280px;
  border-radius: 50%;
  border: 2px dashed var(--portfolio-accent);
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(100, 255, 218, 0.05);
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
}

.image-placeholder::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--portfolio-accent), transparent, var(--portfolio-accent));
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-standard);
  z-index: -1;
}

.image-placeholder:hover::before {
  opacity: 0.3;
}

.placeholder-text {
  color: var(--portfolio-text-secondary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  text-align: center;
}

/* Profile Image Styles */
.image-container {
  width: 280px;
  height: 280px;
  border-radius: 50%;
  border: 2px dashed var(--portfolio-accent);
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(100, 255, 218, 0.05);
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
  overflow: hidden;
}

.image-container::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(45deg, var(--portfolio-accent), transparent, var(--portfolio-accent));
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-standard);
  z-index: -1;
}

.image-container:hover::before {
  opacity: 0.3;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
  border: none;
}

@media (max-width: 767px) {
  .image-placeholder {
    width: 200px;
    height: 200px;
  }

  .image-container {
    width: 200px;
    height: 200px;
  }
}

/* Content Column */
.content-column {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.content-wrapper {
  max-width: 600px;
}

@media (max-width: 767px) {
  .content-wrapper {
    text-align: center;
  }
}

/* Typography */
.hero-name {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  color: var(--portfolio-text-primary);
  margin: 0 0 var(--space-16) 0;
  line-height: var(--line-height-tight);
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--portfolio-text-primary) 0%, var(--portfolio-accent) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
}

.hero-bio {
  font-size: var(--font-size-lg);
  line-height: 1.6;
  color: var(--portfolio-text-secondary);
  margin: 0 0 var(--space-32) 0;
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
}

/* Buttons */
.buttons-container {
  display: flex;
  gap: var(--space-16);
  margin-bottom: var(--space-32);
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
}

@media (max-width: 767px) {
  .buttons-container {
    flex-direction: column;
    align-items: center;
  }
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-12) var(--space-24);
  border-radius: var(--radius-base);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  border: none;
  position: relative;
  overflow: hidden;
  min-width: 140px;
  outline: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

.btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--portfolio-accent);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-standard);
  z-index: -1;
}

.btn--primary {
  background: var(--portfolio-accent);
  color: var(--portfolio-bg);
  box-shadow: 0 4px 20px rgba(100, 255, 218, 0.3);
}

.btn--primary:hover {
  transform: scale(1.05);
  box-shadow: 0 8px 30px rgba(100, 255, 218, 0.4);
}

.btn--primary:active {
  transform: scale(0.95);
}

.btn--outline {
  background: transparent;
  color: var(--portfolio-accent);
  border: 2px solid var(--portfolio-accent);
}

.btn--outline::before {
  background: var(--portfolio-accent);
}

.btn--outline:hover {
  color: var(--portfolio-bg);
  transform: scale(1.05);
  box-shadow: 0 4px 20px rgba(100, 255, 218, 0.2);
}

.btn--outline:hover::before {
  opacity: 1;
}

.btn--outline:active {
  transform: scale(0.95);
}

/* Social Icons */
.social-icons {
  display: flex;
  gap: var(--space-16);
  opacity: 0;
  transform: translateY(50px);
  transition: all 1s cubic-bezier(0.16, 1, 0.3, 1);
}

@media (max-width: 767px) {
  .social-icons {
    justify-content: center;
  }
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--radius-base);
  background: var(--portfolio-surface);
  color: var(--portfolio-text-secondary);
  text-decoration: none;
  transition: all var(--duration-normal) var(--ease-standard);
  border: 1px solid rgba(100, 255, 218, 0.2);
  position: relative;
  overflow: hidden;
  outline: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

.social-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  background: rgba(100, 255, 218, 0.1);
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-standard);
  z-index: -1;
}

.social-icon:hover {
  transform: scale(1.2);
  color: var(--portfolio-accent);
  border-color: var(--portfolio-accent);
  box-shadow: 0 4px 20px rgba(100, 255, 218, 0.3);
}

.social-icon:hover::before {
  opacity: 1;
}

.social-icon svg {
  width: 20px;
  height: 20px;
  transition: transform var(--duration-fast) var(--ease-standard);
  position: relative;
  z-index: 1;
}

/* Animation classes for JavaScript */
.animate-in {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

/* Staggered animation delays with more noticeable timing */
[data-animate="name"].animate-in {
  transition-delay: 0.2s;
}

[data-animate="bio"].animate-in {
  transition-delay: 0.4s;
}

[data-animate="buttons"].animate-in {
  transition-delay: 0.6s;
}

[data-animate="social"].animate-in {
  transition-delay: 0.8s;
}

/* Responsive adjustments */
@media (max-width: 767px) {
  .hero-section {
    padding: var(--space-24) var(--space-16);
  }
  
  .hero-content {
    gap: var(--space-24);
  }
  
  .hero-name {
    font-size: 2.5rem;
    margin-bottom: var(--space-12);
  }
  
  .hero-bio {
    font-size: var(--font-size-base);
    margin-bottom: var(--space-24);
  }
  
  .buttons-container {
    margin-bottom: var(--space-24);
    gap: var(--space-12);
  }
  
  .btn {
    width: 100%;
    max-width: 200px;
  }
}

/* Custom focus styles for accessibility - no blue dots */
.btn:focus,
.social-icon:focus {
  box-shadow: 0 0 0 2px var(--portfolio-accent) !important;
  outline: none !important;
}

/* Remove any potential blue dot sources */
button:focus,
a:focus,
*:focus {
  outline: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Ensure no unwanted visual artifacts */
*, *::before, *::after {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

/* Work Experience Section */
.work-experience-section {
  min-height: 100vh;
  padding: var(--space-32) var(--space-16);
  background: var(--portfolio-bg);
  position: relative;
}

.work-experience-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(ellipse at center, rgba(100, 255, 218, 0.03) 0%, transparent 70%);
  pointer-events: none;
}

.section-title {
  font-size: 3rem;
  font-weight: 700;
  text-align: center;
  margin-bottom: var(--space-32);
  color: var(--portfolio-text-primary);
  position: relative;
  z-index: 1;
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.section-title.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.timeline-container {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  padding: var(--space-24) 0;
}

/* Central Timeline Line */
.timeline-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: rgba(160, 160, 160, 0.3);
  transform: translateX(-50%);
  z-index: 1;
}

/* Timeline Items */
.timeline-item {
  position: relative;
  margin-bottom: var(--space-32);
  opacity: 0;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.timeline-item.animate-in {
  opacity: 1;
}

/* Timeline Markers */
.timeline-marker {
  position: absolute;
  top: var(--space-8);
  left: 50%;
  width: 16px;
  height: 16px;
  background: var(--portfolio-accent);
  border: 3px solid var(--portfolio-bg);
  border-radius: 50%;
  transform: translateX(-50%);
  z-index: 2;
  box-shadow: 0 0 0 4px rgba(100, 255, 218, 0.2);
}

/* Timeline Content */
.timeline-content {
  background: var(--portfolio-surface);
  border: 1px solid rgba(100, 255, 218, 0.1);
  border-radius: 12px;
  padding: var(--space-24);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  position: relative;
}

.timeline-content::before {
  content: '';
  position: absolute;
  top: var(--space-16);
  width: 0;
  height: 0;
  border: 8px solid transparent;
}

/* Left Side Items */
.timeline-item--left {
  transform: translateX(-50px);
}

.timeline-item--left.animate-in {
  transform: translateX(0);
}

.timeline-item--left .timeline-content {
  margin-right: calc(50% + var(--space-24));
}

.timeline-item--left .timeline-content::before {
  right: -16px;
  border-left-color: rgba(100, 255, 218, 0.1);
}

/* Right Side Items */
.timeline-item--right {
  transform: translateX(50px);
}

.timeline-item--right.animate-in {
  transform: translateX(0);
}

.timeline-item--right .timeline-content {
  margin-left: calc(50% + var(--space-24));
}

.timeline-item--right .timeline-content::before {
  left: -16px;
  border-right-color: rgba(100, 255, 218, 0.1);
}

/* Company Header */
.company-header {
  display: flex;
  align-items: center;
  gap: var(--space-12);
  margin-bottom: var(--space-16);
}

.company-logo {
  width: 48px;
  height: 48px;
  background: var(--portfolio-accent);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1rem;
  color: var(--portfolio-bg);
  overflow: hidden;
}

.company-logo-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.company-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--portfolio-text-primary);
  margin: 0;
}

.job-role {
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--portfolio-text-secondary);
  margin: 0 0 var(--space-12) 0;
}

.job-timeline {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  font-size: 0.9rem;
  color: var(--portfolio-text-secondary);
  margin: 0 0 var(--space-16) 0;
}

.calendar-icon {
  color: var(--portfolio-accent);
}

.job-details {
  list-style: none;
  padding: 0;
  margin: 0;
}

.job-details li {
  position: relative;
  padding-left: var(--space-20);
  margin-bottom: var(--space-12);
  color: var(--portfolio-text-primary);
  line-height: 1.6;
}

.job-details li:not(:last-child)::before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--portfolio-accent);
  font-weight: bold;
}

.certificate-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-8) var(--space-16);
  background: rgba(100, 255, 218, 0.1);
  color: var(--portfolio-accent);
  text-decoration: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid rgba(100, 255, 218, 0.2);
}

.certificate-btn:hover {
  background: rgba(100, 255, 218, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(100, 255, 218, 0.2);
}

/* Mobile Responsive Design */
@media (max-width: 767px) {
  .work-experience-section {
    padding: var(--space-24) var(--space-16);
  }

  .section-title {
    font-size: 2.5rem;
    margin-bottom: var(--space-24);
  }

  .timeline-line {
    left: var(--space-16);
  }

  .timeline-marker {
    left: var(--space-16);
  }

  .timeline-item--left .timeline-content,
  .timeline-item--right .timeline-content {
    margin-left: calc(var(--space-16) + var(--space-24));
    margin-right: 0;
  }

  .timeline-item--left .timeline-content::before,
  .timeline-item--right .timeline-content::before {
    left: -16px;
    right: auto;
    border-left-color: transparent;
    border-right-color: rgba(100, 255, 218, 0.1);
  }

  .timeline-item--left,
  .timeline-item--right {
    transform: translateX(-30px);
  }

  .timeline-item--left.animate-in,
  .timeline-item--right.animate-in {
    transform: translateX(0);
  }

  .company-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-8);
  }

  .company-logo {
    width: 40px;
    height: 40px;
    font-size: 0.9rem;
  }

  .company-name {
    font-size: 1.25rem;
  }

  .job-role {
    font-size: 1.1rem;
  }
}

/* Project Work Section */
.project-work-section {
  min-height: 100vh;
  padding: var(--space-32) var(--space-16);
  background: var(--portfolio-bg);
  position: relative;
}

.project-timeline-container {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  padding-left: var(--space-24);
}

/* Vertical Timeline Line */
.project-timeline-line {
  position: absolute;
  left: var(--space-12);
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, var(--portfolio-accent) 0%, rgba(100, 255, 218, 0.3) 100%);
  border-radius: 1px;
}

/* Timeline Item */
.project-timeline-item {
  position: relative;
  margin-bottom: var(--space-32);
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.project-timeline-item.animate-in {
  opacity: 1;
  transform: translateX(0);
}

/* Project Marker (Dot on Timeline) */
.project-marker {
  position: absolute;
  left: calc(-1 * var(--space-12) - 6px);
  top: var(--space-4);
  width: 14px;
  height: 14px;
  background: var(--portfolio-accent);
  border-radius: 50%;
  border: 2px solid var(--portfolio-bg);
  box-shadow: 0 0 0 2px var(--portfolio-accent);
  z-index: 2;
}

/* Project Content Block */
.project-content {
  margin-left: var(--space-24);
  padding-bottom: var(--space-24);
}

/* Project Name */
.project-name {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--portfolio-text-primary);
  margin: 0 0 var(--space-12) 0;
  line-height: var(--line-height-tight);
}

/* Project Purpose */
.project-purpose {
  font-size: var(--font-size-base);
  color: var(--portfolio-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--space-16) 0;
}

/* Tech Stack Pills */
.project-tech-stack {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-8);
  margin-bottom: var(--space-16);
}

.tech-pill {
  display: inline-block;
  padding: var(--space-4) var(--space-12);
  background: rgba(100, 255, 218, 0.1);
  color: var(--portfolio-accent);
  border: 1px solid rgba(100, 255, 218, 0.3);
  border-radius: var(--radius-full);
  font-size: var(--font-size-sm);
  font-weight: 500;
  transition: all var(--duration-normal) var(--ease-standard);
}

.tech-pill:hover {
  background: rgba(100, 255, 218, 0.2);
  border-color: var(--portfolio-accent);
  transform: translateY(-1px);
}

/* Project Learnings */
.project-learnings {
  font-size: var(--font-size-base);
  color: var(--portfolio-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0 0 var(--space-16) 0;
  font-style: italic;
}

/* Project Link */
.project-link {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  color: var(--portfolio-text-secondary);
  background: rgba(100, 255, 218, 0.05);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: var(--radius-md);
  transition: all var(--duration-normal) var(--ease-standard);
  text-decoration: none;
}

.project-link:hover {
  color: var(--portfolio-accent);
  background: rgba(100, 255, 218, 0.1);
  border-color: var(--portfolio-accent);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(100, 255, 218, 0.2);
}

.project-link svg {
  width: 20px;
  height: 20px;
}

/* Project Work Responsive Design */
@media (max-width: 767px) {
  .project-work-section {
    padding: var(--space-24) var(--space-16);
  }

  .project-timeline-container {
    padding-left: var(--space-20);
  }

  .project-timeline-line {
    left: var(--space-10);
  }

  .project-marker {
    left: calc(-1 * var(--space-10) - 6px);
  }

  .project-content {
    margin-left: var(--space-20);
  }

  .project-name {
    font-size: var(--font-size-lg);
  }

  .project-purpose,
  .project-learnings {
    font-size: var(--font-size-sm);
  }

  .tech-pill {
    font-size: var(--font-size-xs);
    padding: var(--space-2) var(--space-8);
  }
}

/* Education & Certificates Section */
.education-certificates-section {
  min-height: 100vh;
  padding: var(--space-32) var(--space-16) var(--space-48) var(--space-16);
  background: var(--portfolio-bg);
  position: relative;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  justify-content: center;
  gap: var(--space-4);
  margin-bottom: var(--space-32);
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.tab-navigation.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.tab-button {
  padding: var(--space-12) var(--space-24);
  background: transparent;
  color: var(--portfolio-text-secondary);
  border: none;
  border-bottom: 2px solid transparent;
  font-size: var(--font-size-base);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
}

.tab-button:hover {
  color: var(--portfolio-text-primary);
}

.tab-button.active {
  color: var(--portfolio-accent);
  border-bottom-color: var(--portfolio-accent);
}

/* Tab Content Container */
.tab-content-container {
  position: relative;
  min-height: 600px;
  overflow: hidden;
}

.tab-content {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  opacity: 0;
  transform: translateX(20px);
  transition: all 0.5s cubic-bezier(0.16, 1, 0.3, 1);
  pointer-events: none;
}

.tab-content.active {
  opacity: 1;
  transform: translateX(0);
  pointer-events: auto;
}

.tab-content.exiting {
  opacity: 0;
  transform: translateX(-20px);
}

/* Timeline View Container */
.timeline-view-container {
  position: relative;
  max-width: 1000px;
  margin: 0 auto;
  padding-left: var(--space-24);
}

/* Vertical Timeline Line */
.vertical-timeline-line {
  position: absolute;
  left: var(--space-12);
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, var(--portfolio-accent) 0%, rgba(100, 255, 218, 0.3) 100%);
  border-radius: 1px;
}

/* Timeline View Item */
.timeline-view-item {
  position: relative;
  margin-bottom: var(--space-32);
  opacity: 0;
  transform: translateX(50px);
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.timeline-view-item.animate-in {
  opacity: 1;
  transform: translateX(0);
}

/* Timeline View Marker */
.timeline-view-marker {
  position: absolute;
  left: calc(-1 * var(--space-12) - 6px);
  top: var(--space-4);
  width: 14px;
  height: 14px;
  background: var(--portfolio-accent);
  border-radius: 50%;
  border: 2px solid var(--portfolio-bg);
  box-shadow: 0 0 0 2px var(--portfolio-accent);
  z-index: 2;
}

/* Timeline View Content */
.timeline-view-content {
  margin-left: var(--space-24);
  padding-bottom: var(--space-24);
}

/* Certificate Styles */
.certificate-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--portfolio-text-primary);
  margin: 0 0 var(--space-12) 0;
  line-height: var(--line-height-tight);
}

.certificate-details {
  display: flex;
  align-items: center;
  gap: var(--space-16);
  flex-wrap: wrap;
}

.certificate-logo {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(100, 255, 218, 0.1);
  color: var(--portfolio-accent);
  border: 1px solid rgba(100, 255, 218, 0.3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  font-weight: 600;
  overflow: hidden;
}

.certificate-logo-img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: var(--radius-md);
}

.certificate-issuer {
  font-size: var(--font-size-base);
  color: var(--portfolio-text-secondary);
  font-weight: 500;
}

.certificate-platform {
  font-size: var(--font-size-sm);
  color: var(--portfolio-text-secondary);
  background: rgba(100, 255, 218, 0.05);
  padding: var(--space-4) var(--space-8);
  border-radius: var(--radius-sm);
  margin-left: auto;
}

/* Education Styles */
.education-degree {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--portfolio-text-primary);
  margin: 0 0 var(--space-8) 0;
  line-height: var(--line-height-tight);
}

.education-institution {
  font-size: var(--font-size-base);
  color: var(--portfolio-text-secondary);
  margin: 0 0 var(--space-8) 0;
  font-weight: 500;
}

.education-details {
  font-size: var(--font-size-sm);
  color: var(--portfolio-text-secondary);
  margin: 0;
  font-style: italic;
}

/* Education & Certificates Responsive Design */
@media (max-width: 767px) {
  .education-certificates-section {
    padding: var(--space-24) var(--space-16);
  }

  .tab-navigation {
    gap: var(--space-2);
    margin-bottom: var(--space-24);
  }

  .tab-button {
    padding: var(--space-8) var(--space-16);
    font-size: var(--font-size-sm);
  }

  .timeline-view-container {
    padding-left: var(--space-20);
  }

  .vertical-timeline-line {
    left: var(--space-10);
  }

  .timeline-view-marker {
    left: calc(-1 * var(--space-10) - 6px);
  }

  .timeline-view-content {
    margin-left: var(--space-20);
  }

  .certificate-name,
  .education-degree {
    font-size: var(--font-size-base);
  }

  .certificate-details {
    gap: var(--space-8);
  }

  .certificate-logo {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-xs);
  }

  .certificate-platform {
    margin-left: 0;
    margin-top: var(--space-4);
  }

  .education-institution {
    font-size: var(--font-size-sm);
  }

  .education-details {
    font-size: var(--font-size-xs);
  }
}

/* Skills Section */
.skills-section {
  min-height: 100vh;
  padding: var(--space-48) var(--space-16);
  margin-top: var(--space-32);
  background: var(--portfolio-bg);
  position: relative;
}

/* Skills Header */
.skills-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--space-32);
}

/* Skills Content Layout */
.skills-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--space-32);
  max-width: 1200px;
  margin: 0 auto;
}

@media (min-width: 768px) {
  .skills-content {
    grid-template-columns: 2fr 3fr;
    gap: var(--space-48);
  }
}

/* Left Column: Skills Timeline */
.skills-timeline-column {
  position: relative;
}

.skills-timeline-container {
  position: relative;
  padding-left: var(--space-24);
}

/* Skills Timeline Icon */
.skills-timeline-icon {
  position: absolute;
  left: calc(var(--space-12) - 22px);
  top: -15px;
  z-index: 3;
  background: var(--portfolio-bg);
  border-radius: 50%;
  padding: var(--space-8);
  box-shadow: 0 0 0 2px var(--portfolio-accent);
}

.skills-icon {
  color: var(--portfolio-accent);
  width: 32px;
  height: 32px;
  display: block;
}

/* Skills Timeline Line */
.skills-timeline-line {
  position: absolute;
  left: var(--space-12);
  top: 24px;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, var(--portfolio-accent) 0%, rgba(100, 255, 218, 0.3) 100%);
  border-radius: 1px;
}

/* Skills Timeline Item */
.skills-timeline-item {
  position: relative;
  margin-bottom: var(--space-32);
  opacity: 0;
  transform: translateX(30px);
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.skills-timeline-item.animate-in {
  opacity: 1;
  transform: translateX(0);
}

/* Skills Timeline Marker */
.skills-timeline-marker {
  position: absolute;
  left: calc(-1 * var(--space-12) - 6px);
  top: var(--space-4);
  width: 14px;
  height: 14px;
  background: var(--portfolio-accent);
  border-radius: 50%;
  border: 2px solid var(--portfolio-bg);
  box-shadow: 0 0 0 2px var(--portfolio-accent);
  z-index: 2;
}

/* Skills Timeline Content */
.skills-timeline-content {
  margin-left: var(--space-24);
  padding-bottom: var(--space-16);
}

.skill-category-name {
  font-size: var(--font-size-lg);
  font-weight: 600;
  color: var(--portfolio-text-primary);
  margin: 0 0 var(--space-8) 0;
  line-height: var(--line-height-tight);
}

.skill-category-list {
  font-size: var(--font-size-base);
  color: var(--portfolio-text-secondary);
  line-height: var(--line-height-relaxed);
  margin: 0;
}



/* Skills Section Responsive Design */
@media (max-width: 767px) {
  .skills-section {
    padding: var(--space-24) var(--space-16);
  }

  .skills-header {
    margin-bottom: var(--space-24);
  }

  .skills-timeline-container {
    padding-left: var(--space-20);
  }

  .skills-timeline-icon {
    left: calc(var(--space-10) - 16px);
    padding: var(--space-6);
  }

  .skills-icon {
    width: 24px;
    height: 24px;
  }

  .skills-timeline-line {
    left: var(--space-10);
    top: 20px;
  }

  .skills-timeline-marker {
    left: calc(-1 * var(--space-10) - 6px);
  }

  .skills-timeline-content {
    margin-left: var(--space-20);
  }

  .skill-category-name {
    font-size: var(--font-size-base);
  }

  .skill-category-list {
    font-size: var(--font-size-sm);
  }
}

/* Contact Section */
.contact-section {
  min-height: 80vh;
  padding: var(--space-48) var(--space-16) var(--space-24) var(--space-16);
  background: var(--portfolio-bg);
  position: relative;
}

.contact-section .section-title {
  text-align: center;
  margin-bottom: var(--space-48);
  opacity: 1;
  transform: translateY(0);
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.contact-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-64);
  max-width: 1200px;
  margin: 0 auto;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1) 0.2s;
}

.contact-layout.animate-in {
  opacity: 1;
  transform: translateY(0);
}

/* Left Column - Contact Information */
.contact-info-column {
  display: flex;
  flex-direction: column;
  gap: var(--space-24);
}

.contact-info-title {
  font-size: var(--font-size-xl);
  font-weight: 600;
  color: var(--portfolio-text-primary);
  margin-bottom: var(--space-16);
}

.contact-info-item {
  display: flex;
  flex-direction: column;
  gap: var(--space-8);
}

.contact-info-label {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--portfolio-text-secondary);
}

.contact-info-value {
  font-size: var(--font-size-lg);
  color: var(--portfolio-accent);
  text-decoration: none;
  transition: color var(--duration-normal) var(--ease-standard);
}

.contact-info-value:hover {
  color: var(--portfolio-text-primary);
}

.contact-buttons {
  display: flex;
  gap: var(--space-16);
  margin-top: var(--space-24);
}

.contact-button {
  display: flex;
  align-items: center;
  gap: var(--space-8);
  padding: var(--space-12) var(--space-20);
  background: rgba(100, 255, 218, 0.1);
  border: 1px solid var(--portfolio-accent);
  border-radius: var(--radius-md);
  color: var(--portfolio-accent);
  text-decoration: none;
  font-size: var(--font-size-base);
  font-weight: 500;
  transition: all var(--duration-normal) var(--ease-standard);
}

.contact-button:hover {
  background: var(--portfolio-accent);
  color: var(--portfolio-bg);
  transform: translateY(-2px);
}

/* Right Column - Social Links */
.contact-social-column {
  display: flex;
  align-items: center;
  justify-content: center;
}

.contact-social-links {
  display: flex;
  flex-direction: column;
  gap: var(--space-20);
  width: 100%;
  max-width: 300px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-16) var(--space-20);
  background: rgba(100, 255, 218, 0.05);
  border: 1px solid rgba(100, 255, 218, 0.2);
  border-radius: var(--radius-md);
  color: var(--portfolio-text-primary);
  text-decoration: none;
  font-size: var(--font-size-lg);
  font-weight: 500;
  transition: all var(--duration-normal) var(--ease-standard);
  position: relative;
  overflow: hidden;
}

.social-link::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, var(--portfolio-accent), rgba(100, 255, 218, 0.8));
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-standard);
  z-index: -1;
}

.social-link:hover {
  color: var(--portfolio-bg);
  transform: translateX(8px);
  border-color: var(--portfolio-accent);
}

.social-link:hover::before {
  opacity: 1;
}

.social-link-text {
  font-weight: 600;
}

.social-link-icon {
  color: var(--portfolio-accent);
  transition: color var(--duration-normal) var(--ease-standard);
}

.social-link:hover .social-link-icon {
  color: var(--portfolio-bg);
}

/* Footer Section */
.footer-section {
  padding: var(--space-16) var(--space-16) var(--space-32) var(--space-16);
  background: linear-gradient(135deg, var(--portfolio-bg) 0%, var(--portfolio-secondary) 100%);
  border-top: 1px solid rgba(100, 255, 218, 0.1);
  position: relative;
}

.footer-content {
  text-align: center;
  opacity: 1;
  transform: translateY(0);
  transition: all 0.8s cubic-bezier(0.16, 1, 0.3, 1);
}

.footer-content.animate-in {
  opacity: 1;
  transform: translateY(0);
}

.footer-name {
  font-size: var(--font-size-base);
  font-weight: 500;
  color: var(--portfolio-text-primary);
  margin-bottom: var(--space-8);
  margin: 0 0 var(--space-8) 0;
}

.footer-copyright {
  font-size: var(--font-size-sm);
  color: var(--portfolio-text-secondary);
  margin: 0;
}

/* Contact and Footer Responsive Design */
@media (max-width: 1024px) {
  .contact-layout {
    gap: var(--space-48);
  }

  .contact-buttons {
    flex-direction: column;
  }

  .contact-button {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .contact-section {
    padding: var(--space-32) var(--space-16) var(--space-16) var(--space-16);
    min-height: 60vh;
  }

  .contact-layout {
    grid-template-columns: 1fr;
    gap: var(--space-40);
    text-align: center;
  }

  .contact-info-title {
    font-size: var(--font-size-lg);
  }

  .contact-info-item {
    align-items: center;
  }

  .contact-buttons {
    justify-content: center;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .contact-button {
    font-size: var(--font-size-sm);
    padding: var(--space-10) var(--space-16);
  }

  .social-link {
    font-size: var(--font-size-base);
    padding: var(--space-12) var(--space-16);
  }
}

@media (max-width: 480px) {
  .contact-section .section-title {
    margin-bottom: var(--space-32);
  }

  .contact-layout {
    gap: var(--space-32);
  }

  .contact-buttons {
    flex-direction: column;
    gap: var(--space-12);
  }

  .contact-social-links {
    gap: var(--space-16);
  }

  .social-link {
    padding: var(--space-10) var(--space-12);
    font-size: var(--font-size-sm);
  }

  .footer-section {
    padding: var(--space-12) var(--space-16) var(--space-24) var(--space-16);
  }

  .footer-name {
    font-size: var(--font-size-sm);
  }

  .footer-copyright {
    font-size: var(--font-size-xs);
  }
}

